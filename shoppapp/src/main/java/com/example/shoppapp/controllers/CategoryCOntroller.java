package com.example.shoppapp.controllers;

import com.example.shoppapp.dtos.CategoryDTO;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("api/v1/categories")
//http://localhost:8088/api/v1/categories
public class CategoryCOntroller {
    @GetMapping("")
    public ResponseEntity<String> getAllcategoeies(
    @RequestParam("page")  int page,
    @RequestParam("limit")  int limit
    )
    {
        return ResponseEntity.ok(String.format("Get categories page %d limit %d",page,limit));
    }

    @PostMapping("")
    public ResponseEntity<?> insertcategories(@RequestBody @Valid CategoryDTO categoryDTO,
                                                   BindingResult result
    )
    {
        if (result.hasErrors())
        {
            List<String> errorMessages = result.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .toList();
            return ResponseEntity.badRequest().body(errorMessages);
        }
        return ResponseEntity.ok("Post_categories"+categoryDTO);
    }

    @PutMapping("/{id}")
    public ResponseEntity<String> updatecategories(@PathVariable Long id){
        return ResponseEntity.ok("Update categories "+ id);
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<String> deletecategories(@PathVariable Long id){
        return ResponseEntity.ok("Deletecategories "+ id);
    }
}

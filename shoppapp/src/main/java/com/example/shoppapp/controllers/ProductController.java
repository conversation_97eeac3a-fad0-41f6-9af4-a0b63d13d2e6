package com.example.shoppapp.controllers;

import com.example.shoppapp.dtos.ProductDTO;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/products")
public class ProductController {
    @GetMapping("")
    //https:localhost:8088/api/v1/product?page=1&limit=2
    public ResponseEntity<String> getProducts(
            @RequestParam("page") int page,
            @RequestParam("limit") int limit
    )
    {
        return ResponseEntity.ok(String.format("getProducts successfully page: %d limit :%d ",page,limit));
    }

    @GetMapping("/{id}")
    public ResponseEntity<String> getProductsId(
            @PathVariable Long id
    )
    {
        return ResponseEntity.ok("getProductsId "+id);
    }

    @PostMapping()
    public ResponseEntity<?> createProduct(
           @RequestBody @Valid ProductDTO productDTO, BindingResult result
    )
    {
        if (result.hasErrors()) {
            List<String> errorMessages = result.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .toList();
            return ResponseEntity.badRequest().body(errorMessages);
        }

        return ResponseEntity.ok("Post_categories"+productDTO);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteProduct(@PathVariable Long id){
        return ResponseEntity.ok("deleteProduct "+ id);
    }
}

package com.example.shoppapp.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductDTO {
    @NotEmpty(message = "name cannot be empty")
    @Size(min=3, max=200, message = "name must be between 3 and 200 characters")
    private String name;

    @Min(value =0, message = "price must be greater than or equal to 0")
    @Max(value=10000000, message = "price must be less than or equal to 10,000,000")
    private float price;
    private String description;
    private String thumbnail;

    @JsonProperty("category_id")
    private int categoryId;
    private MultipartFile file;



}

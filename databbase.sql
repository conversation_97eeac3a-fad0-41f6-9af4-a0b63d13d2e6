CREATE DATABASE ShoppApp;
USE ShoppApp;

CREATE TABLE users(
    id INT Primary Key AUTO_INCREMENT ,
    fullname VARCHAR(100) DEFAULT '',
    phone_number VARCHAR(20) NOT NULL,
    address VARCHAR(200) DEFAULT '',
    password VARCHAR(100) NOT NULL DEFAULT '', 
    created_at DATETIME,
    updated_at DATETIME,
    is_active TINYINT(1) DEFAULT 1,
    date_of_birth DATE,
    facebook_account_id INT DEFAULT 0,
    google_account_id INT DEFAULT 0,
    avatar VARCHAR(200) DEFAULT ''
);
-- phan quy<PERSON>
ALTER TABLE users ADD COLUMN role_id INT ;

CREATE TABLE roles(
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(20) NOT NULL
);
ALTER TABLE users ADD FOREIGN KEY (role_id) REFERENCES roles(id);


CREATE TABLE tokens(
    id INT PRIMARY KEY AUTO_INCREMENT,
    token VARCHAR(255) UNIQUE NOT NULL,
    token_type VARCHAR(50) NOT NULL,
    expiration_date DATETIME,
    revoked tinyint(1) NOT NULL,
    expired tinyint(1) NOT NULL,    
    user_id int,
    Foreign Key (user_id) REFERENCES users(id)
);

-- ho tro dang nhap fb va gg

CREATE TABLE social_accounts(
    id INT PRIMARY KEY AUTO_INCREMENT,
    provider VARCHAR(20) NOT NULL COMMENT 'ten nha social network',
    provider_id VARCHAR(50) NOT NULL ,
    email VARCHAR(150) NOT NULL COMMENT 'Email tai khoan',
    name VARCHAR(100) NOT NULL COMMENT 'Ten tai khoan',
    user_id int,
    Foreign Key (user_id) REFERENCES users(id)
);

-- bang danh muc san phan (category)
CREATE TABLE categories(
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'ten danh muc vd: do dien tu'
);

-- bang chua san phan (product)
CREATE TABLE products(
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(300) COMMENT 'ten san phan vd: may tinh',
    price FLOAT NOT NULL CHECK(price >= 0),
    description LONGTEXT DEFAULT '' ,
    thumbnail VARCHAR(200) DEFAULT '',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    category_id INT,
    Foreign Key (category_id) REFERENCES categories(id)
    );

--orders
CREATE TABLE orders(
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    Foreign Key (user_id) REFERENCES users(id),
    fullname VARCHAR(100) DEFAULT '',
    phone_number VARCHAR(20) NOT NULL,
    address VARCHAR(200) NOT NULL,
    note VARCHAR(200) DEFAULT '',
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending',
    total_money FLOAT CHECK(total_money>=0)
);
ALTER TABLE orders ADD COLUMN shipping_method VARCHAR(100);
ALTER TABLE orders ADD COLUMN shipping_address VARCHAR(200); 
ALTER TABLE orders ADD COLUMN shipping_date DATE;
ALTER TABLE orders ADD COLUMN tracking_number VARCHAR(100);
ALTER TABLE orders ADD COLUMN payment_method VARCHAR(100);

-- xoa don hang => xoa mem => them truong active
ALTER TABLE orders ADD COLUMN active TINYINT(1);
-- trang thai don hang chi cho phep 1 so gia tri cu the 
ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') COMMENT 'trang thai don hang';

CREATE TABLE order_details(
id INT PRIMARY KEY AUTO_INCREMENT,
order_id INT,
Foreign Key (order_id) REFERENCES orders(id),
product_id INT,
Foreign Key (product_id) REFERENCES products(id),
price FLOAT NOT NULL CHECK(price >= 0),
number_of_products INT CHECK (number_of_products > 0),
total_money FLOAT CHECK(total_money >= 0),
color VARCHAR(100) DEFAULT ''
);
